_PLEASE NOTE the following text from the iperf3 license.  Submitting a
pull request to the iperf3 repository constitutes "[making]
Enhancements available...publicly":_

```
You are under no obligation whatsoever to provide any bug fixes, patches, or
upgrades to the features, functionality or performance of the source code
("Enhancements") to anyone; however, if you choose to make your Enhancements
available either publicly, or directly to Lawrence Berkeley National
Laboratory, without imposing a separate written license agreement for such
Enhancements, then you hereby grant the following license: a non-exclusive,
royalty-free perpetual license to install, use, modify, prepare derivative
works, incorporate into other computer software, distribute, and sublicense
such enhancements or derivative works thereof, in binary and source code form.
```

_The complete iperf3 license is available in the `LICENSE` file in the
top directory of the iperf3 source tree._

* Version of iperf3 (or development branch, such as `master` or
  `3.1-STABLE`) to which this pull request applies:

* Issues fixed (if any):

* Brief description of code changes (suitable for use as a commit message):

