_NOTE: The iperf3 issue tracker is for registering bugs, enhancement
requests, or submissions of code.  It is not a means for asking
questions about building or using iperf3.  Those are best directed
towards the Discussions section for this project at
https://github.com/esnet/iperf/discussions
or to the iperf3 mailing <NAME_EMAIL>.
A list of frequently-asked questions
regarding iperf3 can be found at http://software.es.net/iperf/faq.html._

# Context

* Version of iperf3:

* Hardware:

* Operating system (and distribution, if any):

_Please note: iperf3 is supported on Linux, FreeBSD, and macOS.
Support may be provided on a best-effort basis to other UNIX-like
platforms.  We cannot provide support for building and/or running
iperf3 on Windows, iOS, or Android._

* Other relevant information (for example, non-default compilers,
  libraries, cross-compiling, etc.):

_Please fill out one of the "Bug Report" or "Enhancement Request"
sections, as appropriate. Note that submissions of bug fixes, new
features, etc. should be done as a pull request at
https://github.com/esnet/iperf/pulls_

# Bug Report

* Expected Behavior

* Actual Behavior

* Steps to Reproduce

* Possible Solution

# Enhancement Request

* Current behavior

* Desired behavior

* Implementation notes

