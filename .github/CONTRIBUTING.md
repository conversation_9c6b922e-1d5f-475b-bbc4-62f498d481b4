# iperf3 Contribution Guidelines

Thanks for contributing to iperf3!

This page contains some guidelines for filing issues, pull requests,
and other sorts of interactions with the iperf3 project.  These are
guidelines and not hard rules, and it is intended that common sense
and good judgement will prevail.

## Support

iperf3 is officially supported on Linux (various distributions),
FreeBSD, and macOS.  Support may be provided on a best-effort basis to
other UNIX-like platforms.  We cannot provide support for building
and/or running iperf3 on Windows, iOS, or Android.

Before asking for help, please check with your favorite search engine
or the
[iperf3 Discussions site on GitHub](http://github.com/esnet/iperf/discussions)
to see if your question might have been asked (and maybe even
answered) before.  https://fasterdata.es.net/ has some information on
the use of various bandwidth measurement tools, including iperf3.  The
iperf3 documentation Web site at http://software.es.net/iperf/
contains various bits of helpful information, including a list of
[frequently-asked questions](http://software.es.net/iperf/faq.html).

We specifically discourage the use of the issue tracker on the iperf3
GitHub project page for asking questions.  Questions posted in the
form of issues may go unanswered.  Please use the
[iperf3 Discussions site on GitHub](http://github.com/esnet/iperf/discussions)
to ask questions of the community or
alternatively use the iperf3 mailing list at
<EMAIL> (posting requires joining the list).

## Code

If you have improvements or bugfixes to make to iperf3, we'd love to
hear from you.  We prefer changes to be submitted in the form of pull
requests on GitHub, although we can (probably) accept simple patches
as well.  If in doubt, ask.

Before making any submission to the iperf3 project (whether it be code
or documentation), we urge you to consult the iperf3 license, in
particular the section quoted below:

```
You are under no obligation whatsoever to provide any bug fixes, patches, or
upgrades to the features, functionality or performance of the source code
("Enhancements") to anyone; however, if you choose to make your Enhancements
available either publicly, or directly to Lawrence Berkeley National
Laboratory, without imposing a separate written license agreement for such
Enhancements, then you hereby grant the following license: a non-exclusive,
royalty-free perpetual license to install, use, modify, prepare derivative
works, incorporate into other computer software, distribute, and sublicense
such enhancements or derivative works thereof, in binary and source code form.
```

If you're considering changes that will have an architectural impact,
we strongly encourage discussing them with the iperf3 maintainers
before doing a significant amount of work on the code.  We might be
able to provide some guidance.  Also, we're more likely to accept a
submission if if it doesn't involve rewriting large sections of the
code.  Even if you're going to fork the code and maintain your own
changes privately (which you're perfectly welcome to do) we might able
to give you guidance so that future iperf3 changes won't conflict with
your work.

## Conduct

We expect that iperf3 interactions via the issue tracker, mailing
lists, and so forth will be conducted civilly.  Language that is
deemed appropriate or abusive may be removed, and we reserve the right
to ban users from accessing the project for repeated offense.
