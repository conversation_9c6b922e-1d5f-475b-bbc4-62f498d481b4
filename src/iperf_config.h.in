/* src/iperf_config.h.in.  Generated from configure.ac by autoheader.  */

/* Define to 1 if you have the 'clock_gettime' function. */
#undef HAVE_CLOCK_GETTIME

/* Define to 1 if you have the 'clock_nanosleep' function. */
#undef HAVE_CLOCK_NANOSLEEP

/* Define to 1 if you have the 'cpuset_setaffinity' function. */
#undef HAVE_CPUSET_SETAFFINITY

/* Have CPU affinity support. */
#undef HAVE_CPU_AFFINITY

/* Define to 1 if you have the 'daemon' function. */
#undef HAVE_DAEMON

/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H

/* Have IP_MTU_DISCOVER/IP_DONTFRAG/IP_DONTFRAGMENT sockopt. */
#undef HAVE_DONT_FRAGMENT

/* Define to 1 if you have the <endian.h> header file. */
#undef HAVE_ENDIAN_H

/* Have IPv6 flowlabel support. */
#undef HAVE_FLOWLABEL

/* Define to 1 if you have the 'getline' function. */
#undef HAVE_GETLINE

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Have MPTCP protocol. */
#undef HAVE_IPPROTO_MPTCP

/* Have IP_DONTFRAG sockopt. */
#undef HAVE_IP_DONTFRAG

/* Have IP_DONTFRAGMENT sockopt. */
#undef HAVE_IP_DONTFRAGMENT

/* Have IP_MTU_DISCOVER sockopt. */
#undef HAVE_IP_MTU_DISCOVER

/* Define to 1 if you have the <linux/tcp.h> header file. */
#undef HAVE_LINUX_TCP_H

/* Have MSG_TRUNC recv option. */
#undef HAVE_MSG_TRUNC

/* Define to 1 if you have the 'nanosleep' function. */
#undef HAVE_NANOSLEEP

/* Define to 1 if you have the <netinet/sctp.h> header file. */
#undef HAVE_NETINET_SCTP_H

/* Define to 1 if you have the <poll.h> header file. */
#undef HAVE_POLL_H

/* Define if you have POSIX threads libraries and header files. */
#undef HAVE_PTHREAD

/* Have PTHREAD_PRIO_INHERIT. */
#undef HAVE_PTHREAD_PRIO_INHERIT

/* Define to 1 if you have the 'sched_setaffinity' function. */
#undef HAVE_SCHED_SETAFFINITY

/* Have SCTP support. */
#undef HAVE_SCTP_H

/* Define to 1 if you have the 'sendfile' function. */
#undef HAVE_SENDFILE

/* Define to 1 if you have the 'SetProcessAffinityMask' function. */
#undef HAVE_SETPROCESSAFFINITYMASK

/* Have SO_BINDTODEVICE sockopt. */
#undef HAVE_SO_BINDTODEVICE

/* Have SO_MAX_PACING_RATE sockopt. */
#undef HAVE_SO_MAX_PACING_RATE

/* OpenSSL Is Available */
#undef HAVE_SSL

/* Define to 1 if you have the <stdatomic.h> header file. */
#undef HAVE_STDATOMIC_H

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdio.h> header file. */
#undef HAVE_STDIO_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if the system has the type 'struct sctp_assoc_value'. */
#undef HAVE_STRUCT_SCTP_ASSOC_VALUE

/* Define to 1 if you have the <sys/endian.h> header file. */
#undef HAVE_SYS_ENDIAN_H

/* Define to 1 if you have the <sys/socket.h> header file. */
#undef HAVE_SYS_SOCKET_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Have TCP_CONGESTION sockopt. */
#undef HAVE_TCP_CONGESTION

/* Have tcpi_snd_wnd field in tcp_info. */
#undef HAVE_TCP_INFO_SND_WND

/* Have TCP_KEEPIDLE sockopt. */
#undef HAVE_TCP_KEEPALIVE

/* Have TCP_USER_TIMEOUT sockopt. */
#undef HAVE_TCP_USER_TIMEOUT

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* Define to the sub-directory where libtool stores uninstalled libraries. */
#undef LT_OBJDIR

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
#undef PTHREAD_CREATE_JOINABLE

/* Define to 1 if all of the C89 standard headers exist (not just the ones
   required in a freestanding environment). This macro is provided for
   backward compatibility; new code need not use it. */
#undef STDC_HEADERS

/* Version number of package */
#undef VERSION

/* Define to empty if 'const' does not conform to ANSI C. */
#undef const
