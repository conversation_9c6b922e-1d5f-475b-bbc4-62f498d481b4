{% extends "basic/layout.html" %}

{% set theme_css_files = [] %}
{% if theme_bootstrap_version == "3" %}
  {% set bootstrap_version, bootstrap_additional_css, navbar_version = "3.3.6", "theme", "" %}
  {% set bs_span_prefix = "col-md-" %}
{% else %}
  {% set bootstrap_version, bootstrap_additional_css, navbar_version = "2.3.2", "responsive", "-2" %}
  {% set bs_span_prefix = "span" %}
{% endif %}

{% set theme_css_files = theme_css_files + [
    '_static/bootstrap-' + bootstrap_version + '/css/bootstrap.min.css',
    '_static/bootstrap-sphinx.css',
    '_static/esnet/esnet.css'
  ]
%}

{% if not bootswatch_css_custom %}
  {% set bootswatch_css_custom = [] %}
{% endif %}
{% set css_files = css_files + theme_css_files + bootswatch_css_custom %}

{% set script_files = script_files + [
    '_static/js/jquery-1.11.0.min.js',
    '_static/js/jquery-fix.js',
    '_static/bootstrap-' + bootstrap_version + '/js/bootstrap.min.js',
    '_static/bootstrap-sphinx.js'
  ]
%}

{%- set render_sidebar = (not embedded) and (not theme_nosidebar|tobool) and sidebars %}
{# set render_sidebar = True %}
{%- set sidebars = ['localtoc.html', 'sourcelink.html', 'searchbox.html'] #}
{%- set bs_content_width = render_sidebar and "9" or "9"%}

{%- block doctype -%}
<!DOCTYPE html>
{%- endblock %}

{# Sidebar: Rework into our Bootstrap nav section. #}
{% macro navBar() %}
{% include "navbar" + navbar_version + ".html" %}
{% endmacro %}

{% if theme_bootstrap_version == "3" %}
  {%- macro bsidebar() %}
      {%- if render_sidebar %}
      <div class="{{ bs_span_prefix }}3">
        <div id="sidebar" class="bs-sidenav" role="complementary">
          {%- for sidebartemplate in sidebars %}
            {%- include sidebartemplate %}
          {%- endfor %}
        </div>
      </div>
      {%- else %}
      <div class="{{ bs_span_prefix }}1"></div>
      {%- endif %}
  {%- endmacro %}
{% else %}
  {%- macro bsidebar() %}
      {%- if render_sidebar %}
      <div class="{{ bs_span_prefix }}3">
        <div id="sidebar" class="bs-sidenav well" data-spy="affix">
          {%- for sidebartemplate in sidebars %}
            {%- include sidebartemplate %}
          {%- endfor %}
        </div>
      </div>
      {%- endif %}
  {%- endmacro %}
{% endif %}

{%- block extrahead %}
<meta charset='utf-8'>
<meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'>
<meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1'>
<meta name="apple-mobile-web-app-capable" content="yes">
{% endblock %}

{# Silence the sidebar's, relbar's #}
{% block header %}{% endblock %}
{% block relbar1 %}{% endblock %}
{% block relbar2 %}{% endblock %}
{% block sidebarsourcelink %}{% endblock %}

{%- block content %}
{{ navBar() }}
<div class="container">
  <div class="row">
    {%- block sidebar1 %}{{ bsidebar() }}{% endblock %}
    <div class="{{ bs_span_prefix }}{{ bs_content_width }}">
      {% block body %}{% endblock %}
    </div>
    {% block sidebar2 %} {# possible location for sidebar #} {% endblock %}
  </div>
</div>
{%- endblock %}

{%- block footer %}
<footer class="footer">
  <div class="container">
    <div class="row">
      <div class="col-md-1">
        <a href="http://www.es.net/"><img src="_static/ESnet_Final_Logos_All_Blue_Circle_Stamp_RGB.png" alt="[ESnet]"></a>
      </div>
      <div class="col-md-11">
        <p class="pull-right">
        <a href="#">Back to top</a>
        {% if theme_source_link_position == "footer" %}
        <br/>
        {% include "sourcelink.html" %}
        {% endif %}
        </p>
        <p>
        {%- if show_copyright %}
          {%- if hasdoc('copyright') %}
            {% trans path=pathto('copyright'), copyright=copyright|e %}&copy; <a href="{{ path }}">Copyright</a> {{ copyright }}.{% endtrans %}<br/>
          {%- else %}
            {% trans copyright=copyright|e %}&copy; Copyright {{ copyright }}.{% endtrans %}<br/>
          {%- endif %}
        {%- endif %}
        {%- if last_updated %}
          {% trans last_updated=last_updated|e %}Last updated on {{ last_updated }}.{% endtrans %}<br/>
        {%- endif %}
        {%- if show_sphinx %}
          {% trans sphinx_version=sphinx_version|e %}Created using <a href="http://sphinx.pocoo.org/">Sphinx</a> {{ sphinx_version }}.{% endtrans %}<br/>
        {%- endif %}
        </p>
      </div>
    </div>
  </div>
</footer>
{%- endblock %}
