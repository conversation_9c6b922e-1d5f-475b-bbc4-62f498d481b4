  <div id="navbar" class="{{ theme_navbar_class }} navbar-default {% if theme_navbar_fixed_top == 'true' -%} navbar-fixed-top{%- endif -%}">
    <div class="container">
      <div class="navbar-header">
        <!-- .btn-navbar is used as the toggle for collapsed navbar content -->
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".nav-collapse">
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="{{ pathto(master_doc) }}">
          {%- block sidebarlogo %}
            {%- if logo %}<img src="{{ pathto('_static/' + logo, 1) }}">{%- endif %}
          {%- endblock %}
          {% if theme_navbar_title -%}{{ theme_navbar_title|e }}{%- else -%}{{ project|e }}{%- endif -%}
        </a>
      </div>

        <div class="collapse navbar-collapse nav-collapse">
          <ul class="nav navbar-nav">
            {% block navbartoc %}
              {% include "globaltoc.html" %}
              {% if theme_navbar_pagenav %}
                {% include "navbartoc.html" %}
              {% endif %}
            {% endblock %}
            {% if theme_navbar_sidebarrel %}
              {% block sidebarrel %}
                {% include "relations.html" %}
              {% endblock %}
            {% endif %}
            <li class="divider-vertical"></li>
            {% if theme_navbar_links %}
              {%- for link in theme_navbar_links %}
                <li><a href="{{ pathto(*link[1:]) }}">{{ link[0] }}</a></li>
              {%- endfor %}
            {% endif %}
            <li><a href="{{ github_url }}"><img src="_static/icon-github.png" height="20" width="20"></a></li>
            {% block navbarextra %}
            {% endblock %}
            {% if theme_source_link_position == "nav" %}
              <li class="hidden-sm">{% include "sourcelink.html" %}</li>
            {% endif %}
          </ul>
          {% block navbarsearch %}
            {% include "navbarsearchbox.html" %}
          {% endblock %}
        </div>
    </div>
  </div>
