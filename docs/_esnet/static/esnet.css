body {
	padding-top: 70px; // bootstrap default
}

.navbar-default {
	background-color: #006495;
	border-color: #004e74;
}
.navbar-default .navbar-brand {
	color: #ecf0f1;
}
.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
	color: #ffffff;
}
.navbar-default .navbar-text {
	color: #ecf0f1;
}
.navbar-default .navbar-nav > li > a {
	color: #ecf0f1;
}
.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
	color: #ffffff;
}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
	color: #ffffff;
	background-color: #004e74;
}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
	color: #ffffff;
	background-color: #004e74;
}
.navbar-default .navbar-toggle {
	border-color: #004e74;
}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
	background-color: #004e74;
}
.navbar-default .navbar-toggle .icon-bar {
	background-color: #ecf0f1;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
	border-color: #ecf0f1;
}
.navbar-default .navbar-link {
	color: #ecf0f1;
}
.navbar-default .navbar-link:hover {
	color: #ffffff;
}

@media (max-width: 767px) {
	.navbar-default .navbar-nav .open .dropdown-menu > li > a {
		color: #ecf0f1;
	}
	.navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
		color: #ffffff;
	}
	.navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
		color: #ffffff;
		background-color: #004e74;
	}
}

/*
 * Vertical divider
 */
.navbar .divider-vertical {
    height: 50px;
    margin: 0 9px;
    border-right: 1px solid #ecf0f1;
    border-left: 1px solid #ecf0f1;
}

.navbar-inverse .divider-vertical {
    border-right-color: #222222;
    border-left-color: #111111;
}

@media (max-width: 767px) {
    .navbar-collapse .nav > .divider-vertical {
            display: none;
    }
}
